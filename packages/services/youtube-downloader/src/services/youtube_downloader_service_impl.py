"""
YouTube downloader service implementation

Main service implementation for YouTube video/audio downloading using yt-dlp.
"""

import asyncio
import time
from pathlib import Path
from typing import Any, Dict, Optional

import yt_dlp
from loguru import logger

from src.interfaces import FileManagerService, URLValidatorService
from src.models.domain import (
    DownloadConfig,
    DownloadResult,
    DownloadStats,
    VideoMetadata,
)


class YouTubeDownloaderServiceImpl:
    """
    Implementation of YouTube downloader service

    Uses yt-dlp for downloading and extracting audio from YouTube videos.
    Implements progress tracking, error handling, and metadata extraction.
    """

    def __init__(
        self, url_validator: URLValidatorService, file_manager: FileManagerService
    ):
        """
        Initialize the YouTube downloader service

        Args:
            url_validator: URL validation service
            file_manager: File management service
        """
        self.url_validator = url_validator
        self.file_manager = file_manager
        self._progress_data: Dict[str, Any] = {}

    async def download_audio(self, config: DownloadConfig) -> DownloadResult:
        """
        Download audio from a YouTube video

        Args:
            config: Download configuration

        Returns:
            DownloadResult with file path and metadata
        """
        start_time = time.time()

        try:
            # Validate URL
            if not await self.validate_url(config.url):
                return DownloadResult(
                    success=False, error_message=f"Invalid YouTube URL: {config.url}"
                )

            # Ensure directories exist
            self.file_manager.ensure_directory_exists(config.output_directory)
            self.file_manager.ensure_directory_exists(config.temp_directory)

            # Get video metadata first
            video_metadata = await self.get_video_info(config.url)

            # Determine output filename
            if config.output_filename:
                filename = self.file_manager.create_safe_filename(
                    config.output_filename
                )
            else:
                filename = self.file_manager.create_safe_filename(
                    video_metadata.title or "unknown_video"
                )

            # Build yt-dlp options
            ydl_opts = self._build_ydl_options(config, filename)

            # Reset progress tracking
            self._progress_data = {}

            # Download using yt-dlp
            logger.info(f"Starting download: {config.url}")

            def run_download():
                with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                    ydl.download([config.url])

            # Run download in thread pool to avoid blocking
            await asyncio.get_event_loop().run_in_executor(None, run_download)

            # Find the downloaded file
            output_path = self._find_downloaded_file(
                config.output_directory, filename, config.audio_format
            )

            if not output_path or not output_path.exists():
                return DownloadResult(
                    success=False,
                    error_message="Download completed but output file not found",
                )

            # Get file size
            file_size = self.file_manager.get_file_size(output_path)

            # Calculate processing time
            processing_time = time.time() - start_time

            # Create download stats from progress data
            download_stats = DownloadStats(
                total_bytes=self._progress_data.get("total_bytes"),
                downloaded_bytes=self._progress_data.get("downloaded_bytes"),
                download_speed=self._progress_data.get("speed"),
                percent_complete=100.0,
                status="completed",
            )

            logger.success(f"Download completed: {output_path}")

            return DownloadResult(
                success=True,
                file_path=output_path,
                file_size=file_size,
                video_metadata=video_metadata,
                processing_time=processing_time,
                download_stats=download_stats,
            )

        except yt_dlp.utils.DownloadError as e:
            error_msg = f"yt-dlp download error: {e}"
            logger.error(error_msg)
            return DownloadResult(
                success=False,
                error_message=error_msg,
                processing_time=time.time() - start_time,
            )
        except Exception as e:
            error_msg = f"Unexpected error during download: {e}"
            logger.error(error_msg)
            return DownloadResult(
                success=False,
                error_message=error_msg,
                processing_time=time.time() - start_time,
            )

    async def get_video_info(self, url: str) -> VideoMetadata:
        """
        Get video information without downloading

        Args:
            url: YouTube video URL

        Returns:
            VideoMetadata with video information
        """
        if not await self.validate_url(url):
            raise ValueError(f"Invalid YouTube URL: {url}")

        try:
            ydl_opts = {
                "quiet": True,
                "no_warnings": True,
                "extract_flat": False,
            }

            def extract_info():
                with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                    return ydl.extract_info(url, download=False)

            # Run in thread pool to avoid blocking
            info = await asyncio.get_event_loop().run_in_executor(None, extract_info)

            return VideoMetadata(
                video_id=info.get("id"),
                title=info.get("title"),
                uploader=info.get("uploader"),
                upload_date=info.get("upload_date"),
                duration=info.get("duration"),
                view_count=info.get("view_count"),
                like_count=info.get("like_count"),
                description=info.get("description"),
                thumbnail_url=info.get("thumbnail"),
                tags=info.get("tags"),
                categories=info.get("categories"),
                is_live=info.get("is_live"),
                availability=info.get("availability"),
                age_restricted=info.get("age_limit", 0) > 0,
            )

        except Exception as e:
            logger.error(f"Failed to extract video info: {e}")
            raise RuntimeError(f"Cannot get video information: {e}") from e

    async def validate_url(self, url: str) -> bool:
        """Validate YouTube URL"""
        return self.url_validator.is_youtube_url(url)

    async def check_availability(self, url: str) -> bool:
        """Check if video is available for download"""
        try:
            metadata = await self.get_video_info(url)
            return metadata.availability != "private"
        except Exception:
            return False

    async def initialize(self) -> None:
        """Initialize the service"""
        logger.info("YouTube downloader service initialized")

    async def cleanup(self) -> None:
        """Cleanup service resources"""
        logger.info("YouTube downloader service cleaned up")

    def _build_ydl_options(
        self, config: DownloadConfig, filename: str
    ) -> Dict[str, Any]:
        """
        Build yt-dlp options from configuration

        Args:
            config: Download configuration
            filename: Output filename

        Returns:
            Dictionary of yt-dlp options
        """
        output_path = str(config.output_directory / filename)

        opts = {
            "quiet": False,
            "no_warnings": False,
            "extract_flat": False,
            "cachedir": str(config.temp_directory / ".cache"),
            "socket_timeout": config.socket_timeout,
            "retries": config.retries,
            "format": "bestaudio/best",
            "postprocessors": [
                {
                    "key": "FFmpegExtractAudio",
                    "preferredcodec": config.audio_format,
                    "preferredquality": config.audio_quality,
                }
            ],
            "outtmpl": output_path,
            "keepvideo": config.keep_video,
            "writethumbnail": config.download_thumbnail,
            "writesubtitles": config.download_subtitles,
            "ignoreerrors": False,
            "no_color": True,
            "noprogress": not config.enable_progress_hooks,
        }

        # Add proxy if configured
        if config.proxy_url:
            opts["proxy"] = config.proxy_url

        # Add progress hook if enabled
        if config.enable_progress_hooks:
            opts["progress_hooks"] = [self._progress_hook]

        return opts

    def _progress_hook(self, d: Dict[str, Any]) -> None:
        """
        Progress hook for yt-dlp downloads

        Args:
            d: Progress information dictionary
        """
        if d["status"] == "downloading":
            self._progress_data.update(
                {
                    "total_bytes": d.get("total_bytes")
                    or d.get("total_bytes_estimate", 0),
                    "downloaded_bytes": d.get("downloaded_bytes", 0),
                    "speed": d.get("speed", 0),
                    "eta": d.get("eta", 0),
                }
            )

            total = self._progress_data.get("total_bytes", 0)
            downloaded = self._progress_data.get("downloaded_bytes", 0)

            if total > 0:
                percent = (downloaded / total) * 100
                speed = self._progress_data.get("speed", 0)
                if speed:
                    speed_mb = speed / (1024 * 1024)
                    logger.info(
                        f"Download progress: {percent:.1f}% | Speed: {speed_mb:.1f} MB/s"
                    )

        elif d["status"] == "finished":
            logger.info("Download finished, processing audio...")
            self._progress_data["status"] = "finished"

    def _find_downloaded_file(
        self, output_dir: Path, filename: str, audio_format: str
    ) -> Optional[Path]:
        """
        Find the downloaded file in the output directory

        Args:
            output_dir: Output directory
            filename: Base filename
            audio_format: Expected audio format

        Returns:
            Path to downloaded file, None if not found
        """
        # Possible file paths to check
        possible_paths = [
            output_dir / f"{filename}.{audio_format}",
            output_dir / f"{filename}.{audio_format}.{audio_format}",
            output_dir / f"{filename}_na.{audio_format}",
        ]

        # Check for files with different extensions
        for ext in [".mp3", ".m4a", ".wav", ".opus", ".ogg"]:
            possible_paths.append(output_dir / f"{filename}{ext}")

        # Find existing file
        for path in possible_paths:
            if path.exists():
                return path

        # If not found, check for newest file in directory
        try:
            files = list(output_dir.glob(f"*{audio_format}"))
            if files:
                return max(files, key=lambda p: p.stat().st_mtime)
        except Exception:
            pass

        return None
