"""
Download API routes

Provides endpoints for YouTube video/audio downloading and video information.
"""

from fastapi import APIRouter, HTTPException
from loguru import logger

from src.adapters import DownloadAdapter
from src.infrastructure import get_container
from src.models.api import DownloadRequest, DownloadResponse, VideoInfoResponse


router = APIRouter(prefix="/download", tags=["download"])


@router.post("/audio", response_model=DownloadResponse)
async def download_audio(request: DownloadRequest) -> DownloadResponse:
    """
    Download audio from YouTube video
    
    Downloads and extracts audio from a YouTube video URL with configurable
    quality and format options. Supports progress tracking and error handling.
    
    Args:
        request: Download request with URL and options
        
    Returns:
        DownloadResponse: Download result with file path and metadata
        
    Raises:
        HTTPException: If download fails or URL is invalid
    """
    container = get_container()
    adapter = DownloadAdapter(container.settings)
    
    try:
        logger.info(f"Starting audio download request: {request.url}")
        
        # Convert API request to domain configuration
        config = adapter.api_request_to_domain_config(request)
        
        # Perform download using service
        result = await container.youtube_downloader_service.download_audio(config)
        
        # Convert domain result to API response
        response = adapter.domain_result_to_api_response(result)
        
        if result.success:
            logger.success(f"Audio download completed: {result.file_path}")
        else:
            logger.error(f"Audio download failed: {result.error_message}")
        
        return response
        
    except ValueError as e:
        error_msg = f"Invalid request: {e}"
        logger.error(error_msg)
        raise HTTPException(status_code=400, detail=error_msg)
        
    except RuntimeError as e:
        error_msg = f"Download failed: {e}"
        logger.error(error_msg)
        raise HTTPException(status_code=500, detail=error_msg)
        
    except Exception as e:
        error_msg = f"Unexpected error: {e}"
        logger.error(error_msg)
        raise HTTPException(status_code=500, detail=error_msg)


@router.get("/info", response_model=VideoInfoResponse)
async def get_video_info(url: str) -> VideoInfoResponse:
    """
    Get YouTube video information without downloading
    
    Extracts metadata from a YouTube video URL including title, duration,
    view count, and other information without downloading the video.
    
    Args:
        url: YouTube video URL
        
    Returns:
        VideoInfoResponse: Video metadata information
        
    Raises:
        HTTPException: If URL is invalid or info extraction fails
    """
    container = get_container()
    adapter = DownloadAdapter(container.settings)
    
    try:
        logger.info(f"Getting video info for: {url}")
        
        # Get video metadata using service
        metadata = await container.youtube_downloader_service.get_video_info(url)
        
        # Convert domain metadata to API response
        response = adapter.domain_metadata_to_api_response(metadata)
        
        logger.success(f"Video info retrieved: {metadata.title}")
        return response
        
    except ValueError as e:
        error_msg = f"Invalid URL: {e}"
        logger.error(error_msg)
        return adapter.error_to_video_info_response(error_msg)
        
    except RuntimeError as e:
        error_msg = f"Failed to get video info: {e}"
        logger.error(error_msg)
        return adapter.error_to_video_info_response(error_msg)
        
    except Exception as e:
        error_msg = f"Unexpected error: {e}"
        logger.error(error_msg)
        return adapter.error_to_video_info_response(error_msg)


@router.post("/validate", response_model=dict[str, bool])
async def validate_url(url: str) -> dict[str, bool]:
    """
    Validate YouTube URL without downloading
    
    Checks if a URL is a valid YouTube URL that can be downloaded.
    Useful for frontend validation before attempting downloads.
    
    Args:
        url: URL to validate
        
    Returns:
        dict: Validation result with boolean flags
    """
    container = get_container()
    
    try:
        logger.info(f"Validating URL: {url}")
        
        # Validate URL format
        is_valid = await container.youtube_downloader_service.validate_url(url)
        
        # Check availability if URL is valid
        is_available = False
        if is_valid:
            is_available = await container.youtube_downloader_service.check_availability(url)
        
        result = {
            "is_valid": is_valid,
            "is_available": is_available,
            "is_supported": is_valid and is_available
        }
        
        logger.info(f"URL validation result: {result}")
        return result
        
    except Exception as e:
        logger.error(f"URL validation error: {e}")
        return {
            "is_valid": False,
            "is_available": False,
            "is_supported": False
        }
