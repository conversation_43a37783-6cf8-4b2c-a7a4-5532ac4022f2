"""
YouTube downloader service interface

Defines the main service interface for YouTube video/audio downloading
following clean architecture principles.
"""

from typing import Protocol, runtime_checkable

from src.models.domain import DownloadConfig, DownloadResult, VideoMetadata


@runtime_checkable
class YouTubeDownloaderService(Protocol):
    """
    Main YouTube downloader service interface

    Provides methods for downloading YouTube videos/audio and extracting
    video information. Follows the Protocol pattern for dependency inversion.
    """

    async def download_audio(self, config: DownloadConfig) -> DownloadResult:
        """
        Download audio from a YouTube video

        Args:
            config: Download configuration containing URL and options

        Returns:
            DownloadResult containing file path and metadata

        Raises:
            ValueError: If URL is invalid or configuration is invalid
            RuntimeError: If download fails
        """
        ...

    async def get_video_info(self, url: str) -> VideoMetadata:
        """
        Get video information without downloading

        Args:
            url: YouTube video URL

        Returns:
            VideoMetadata containing video information

        Raises:
            ValueError: If URL is invalid
            RuntimeError: If info extraction fails
        """
        ...

    async def validate_url(self, url: str) -> bool:
        """
        Validate if URL is a supported YouTube URL

        Args:
            url: URL to validate

        Returns:
            True if URL is valid and supported
        """
        ...

    async def check_availability(self, url: str) -> bool:
        """
        Check if video is available for download

        Args:
            url: YouTube video URL

        Returns:
            True if video is available for download

        Raises:
            ValueError: If URL is invalid
        """
        ...

    async def initialize(self) -> None:
        """
        Initialize the service and its dependencies

        Raises:
            RuntimeError: If initialization fails
        """
        ...

    async def cleanup(self) -> None:
        """
        Cleanup service resources
        """
        ...
