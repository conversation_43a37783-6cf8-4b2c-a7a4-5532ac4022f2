# YouTube Downloader Service

A microservice for downloading YouTube videos and extracting audio using yt-dlp, following clean architecture patterns.

## Features

- **Video Information Retrieval**: Get metadata about YouTube videos
- **Audio Extraction**: Download and extract audio in various formats (MP3, M4A, etc.)
- **Video Download**: Download videos in different qualities
- **Proxy Support**: SOCKS5 proxy support for network restrictions
- **Progress Tracking**: Real-time download progress monitoring
- **Error Handling**: Comprehensive error handling for various YouTube scenarios
- **Clean Architecture**: Follows dependency injection and clean architecture patterns

## Architecture

This service follows the established clean architecture pattern used across the project:

```
src/
├── api/                    # API layer (FastAPI routes)
│   ├── routes/            # Route definitions
│   └── dependencies.py   # Dependency injection setup
├── core/                  # Core configuration and settings
├── models/                # Data models and DTOs
├── interfaces/            # Abstract interfaces
├── services/              # Business logic implementations
├── infrastructure/        # External service integrations and factories
├── adapters/             # Data transformation adapters
└── main.py               # Application entry point
```

## Installation

1. Navigate to the service directory:
```bash
cd packages/services/youtube-downloader
```

2. Install dependencies using Poetry:
```bash
poetry install
```

3. Activate the virtual environment:
```bash
poetry shell
```

## Configuration

The service uses environment-based configuration. Create a `.env` file or set environment variables:

```env
# Service Configuration
HOST=0.0.0.0
PORT=8003
DEBUG=true

# Download Configuration
DOWNLOAD_DIR=./downloads
TEMP_DIR=./temp
AUDIO_FORMAT=mp3
AUDIO_QUALITY=192

# Proxy Configuration (optional)
PROXY_ENABLED=false
PROXY_URL=socks5://127.0.0.1:9090
```

## Usage

### Starting the Service

```bash
# Development mode
poetry run uvicorn src.main:app --host 0.0.0.0 --port 8003 --reload

# Or using the script
poetry run youtube-downloader
```

### API Endpoints

- `GET /health` - Health check
- `GET /info` - Get video information
- `POST /download/audio` - Download audio from YouTube video
- `POST /download/video` - Download video from YouTube
- `GET /downloads/{filename}` - Retrieve downloaded files

### Example Usage

```python
import httpx

# Get video information
response = httpx.get("http://localhost:8003/info?url=https://youtube.com/watch?v=VIDEO_ID")
video_info = response.json()

# Download audio
response = httpx.post("http://localhost:8003/download/audio", json={
    "url": "https://youtube.com/watch?v=VIDEO_ID",
    "format": "mp3",
    "quality": "192"
})
download_result = response.json()
```

## Development

### Running Tests

```bash
poetry run pytest
```

### Code Quality

The service uses Ruff for linting and formatting:

```bash
# Check code quality
poetry run ruff check src/

# Format code
poetry run ruff format src/
```

### Type Checking

```bash
poetry run mypy src/
```

## Dependencies

- **yt-dlp**: YouTube download functionality
- **FastAPI**: Web framework
- **Pydantic**: Data validation
- **Loguru**: Logging
- **httpx**: HTTP client for external API calls

## Error Handling

The service handles various YouTube-specific errors:

- Private videos
- Age-restricted content
- Geo-blocked content
- Network connectivity issues
- Invalid URLs
- Quota exceeded errors

## Integration

This service integrates with the broader Bayan Arabic Learning Platform and can be called by:

- Frontend applications
- Other microservices
- Background processing tasks

## Monitoring

The service provides health check endpoints and structured logging for monitoring and debugging.
