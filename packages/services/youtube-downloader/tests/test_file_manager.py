"""
Tests for file manager service

Tests file operations, directory management, and filename sanitization.
"""

import tempfile
from pathlib import Path

import pytest

from src.services.file_manager_service_impl import FileManagerServiceImpl


class TestFileManagerService:
    """Test cases for file manager service"""
    
    @pytest.fixture
    def file_manager(self):
        """Create file manager service instance"""
        return FileManagerServiceImpl()
    
    @pytest.fixture
    def temp_dir(self):
        """Create temporary directory for testing"""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield Path(temp_dir)
    
    def test_sanitize_filename(self, file_manager):
        """Test filename sanitization"""
        test_cases = [
            ("normal_filename.mp3", "normal_filename.mp3"),
            ("file with spaces.mp3", "file with spaces.mp3"),
            ("file/with\\slashes.mp3", "file_with_slashes.mp3"),
            ("file:with*special?chars.mp3", "file_with_special_chars.mp3"),
            ("file<with>pipes|.mp3", "file_with_pipes_.mp3"),
            ("file\"with'quotes.mp3", "file_with_quotes.mp3"),
            ("file\nwith\nnewlines.mp3", "file_with_newlines.mp3"),
            ("file\twith\ttabs.mp3", "file_with_tabs.mp3"),
            ("", "untitled"),
            ("   ", "untitled"),
            ("...", "untitled"),
            ("CON.mp3", "CON_.mp3"),  # Windows reserved name
            ("PRN.mp3", "PRN_.mp3"),  # Windows reserved name
        ]
        
        for input_name, expected in test_cases:
            result = file_manager.sanitize_filename(input_name)
            assert result == expected, f"Expected '{expected}', got '{result}' for input '{input_name}'"
    
    def test_ensure_directory_exists(self, file_manager, temp_dir):
        """Test directory creation"""
        test_dir = temp_dir / "test_directory"
        
        # Directory should not exist initially
        assert not test_dir.exists()
        
        # Create directory
        file_manager.ensure_directory_exists(test_dir)
        
        # Directory should now exist
        assert test_dir.exists()
        assert test_dir.is_dir()
        
        # Should not raise error if directory already exists
        file_manager.ensure_directory_exists(test_dir)
        assert test_dir.exists()
    
    def test_ensure_directory_exists_nested(self, file_manager, temp_dir):
        """Test nested directory creation"""
        nested_dir = temp_dir / "level1" / "level2" / "level3"
        
        # Create nested directory
        file_manager.ensure_directory_exists(nested_dir)
        
        # All levels should exist
        assert nested_dir.exists()
        assert nested_dir.is_dir()
        assert (temp_dir / "level1").exists()
        assert (temp_dir / "level1" / "level2").exists()
    
    def test_is_directory_writable(self, file_manager, temp_dir):
        """Test directory write permission checking"""
        # Temp directory should be writable
        assert file_manager.is_directory_writable(temp_dir)
        
        # Non-existent directory should return False
        non_existent = temp_dir / "non_existent"
        assert not file_manager.is_directory_writable(non_existent)
    
    def test_get_safe_file_path(self, file_manager, temp_dir):
        """Test safe file path generation"""
        # Test normal case
        safe_path = file_manager.get_safe_file_path(temp_dir, "test_file.mp3")
        expected_path = temp_dir / "test_file.mp3"
        assert safe_path == expected_path
        
        # Test with unsafe filename
        safe_path = file_manager.get_safe_file_path(temp_dir, "unsafe/file\\name.mp3")
        expected_path = temp_dir / "unsafe_file_name.mp3"
        assert safe_path == expected_path
    
    def test_get_unique_file_path(self, file_manager, temp_dir):
        """Test unique file path generation"""
        base_name = "test_file.mp3"
        
        # First call should return original path
        path1 = file_manager.get_unique_file_path(temp_dir, base_name)
        expected_path1 = temp_dir / "test_file.mp3"
        assert path1 == expected_path1
        
        # Create the file to simulate conflict
        path1.touch()
        
        # Second call should return numbered path
        path2 = file_manager.get_unique_file_path(temp_dir, base_name)
        expected_path2 = temp_dir / "test_file_1.mp3"
        assert path2 == expected_path2
        
        # Create second file
        path2.touch()
        
        # Third call should increment number
        path3 = file_manager.get_unique_file_path(temp_dir, base_name)
        expected_path3 = temp_dir / "test_file_2.mp3"
        assert path3 == expected_path3
    
    def test_cleanup_file(self, file_manager, temp_dir):
        """Test file cleanup"""
        test_file = temp_dir / "test_file.txt"
        test_file.write_text("test content")
        
        # File should exist
        assert test_file.exists()
        
        # Cleanup file
        file_manager.cleanup_file(test_file)
        
        # File should be deleted
        assert not test_file.exists()
        
        # Should not raise error for non-existent file
        file_manager.cleanup_file(test_file)
    
    def test_get_file_size(self, file_manager, temp_dir):
        """Test file size calculation"""
        test_file = temp_dir / "test_file.txt"
        content = "test content"
        test_file.write_text(content)
        
        size = file_manager.get_file_size(test_file)
        assert size == len(content.encode('utf-8'))
        
        # Non-existent file should return 0
        non_existent = temp_dir / "non_existent.txt"
        size = file_manager.get_file_size(non_existent)
        assert size == 0
