"""
Integration tests for YouTube downloader service

Tests the complete download workflow with real YouTube videos.
"""

import tempfile
from pathlib import Path

import pytest

from src.infrastructure import get_container
from src.models.domain import DownloadConfig


class TestYouTubeDownloaderIntegration:
    """Integration test cases for YouTube downloader"""
    
    @pytest.fixture
    def temp_dir(self):
        """Create temporary directory for testing"""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield Path(temp_dir)
    
    @pytest.fixture
    def container(self):
        """Get dependency injection container"""
        return get_container()
    
    @pytest.mark.asyncio
    async def test_get_video_info(self, container):
        """Test getting video information without downloading"""
        # Use a well-known, stable YouTube video for testing
        test_url = "https://www.youtube.com/watch?v=dQw4w9WgXcQ"  # Rick Roll - stable test video
        
        try:
            downloader = container.youtube_downloader_service
            metadata = await downloader.get_video_info(test_url)
            
            # Verify basic metadata is present
            assert metadata.video_id == "dQw4w9WgXcQ"
            assert metadata.title is not None
            assert len(metadata.title) > 0
            assert metadata.uploader is not None
            assert metadata.duration is not None
            assert metadata.duration > 0
            
            print(f"Video info test successful:")
            print(f"  Title: {metadata.title}")
            print(f"  Uploader: {metadata.uploader}")
            print(f"  Duration: {metadata.duration} seconds")
            print(f"  View count: {metadata.view_count}")
            
        except Exception as e:
            pytest.skip(f"Video info test skipped due to network/YouTube issue: {e}")
    
    @pytest.mark.asyncio
    async def test_url_validation(self, container):
        """Test URL validation functionality"""
        downloader = container.youtube_downloader_service
        
        # Test valid URL
        valid_url = "https://www.youtube.com/watch?v=dQw4w9WgXcQ"
        is_valid = await downloader.validate_url(valid_url)
        assert is_valid, "Valid YouTube URL should pass validation"
        
        # Test invalid URL
        invalid_url = "https://www.google.com"
        is_invalid = await downloader.validate_url(invalid_url)
        assert not is_invalid, "Invalid URL should fail validation"
        
        print("URL validation test successful")
    
    @pytest.mark.asyncio
    async def test_check_availability(self, container):
        """Test video availability checking"""
        downloader = container.youtube_downloader_service
        
        try:
            # Test with a known public video
            test_url = "https://www.youtube.com/watch?v=dQw4w9WgXcQ"
            is_available = await downloader.check_availability(test_url)
            
            # This should be True for a public video, but might fail due to network issues
            print(f"Availability check result: {is_available}")
            
        except Exception as e:
            pytest.skip(f"Availability test skipped due to network/YouTube issue: {e}")
    
    @pytest.mark.asyncio
    @pytest.mark.slow  # Mark as slow test since it involves actual downloading
    async def test_audio_download_short_video(self, container, temp_dir):
        """Test downloading audio from a short video"""
        # Use a very short video to minimize test time and bandwidth
        # This is a 10-second test video
        test_url = "https://www.youtube.com/watch?v=BaW_jenozKc"  # YouTube test video
        
        try:
            downloader = container.youtube_downloader_service
            
            # Create download configuration
            config = DownloadConfig(
                url=test_url,
                output_directory=temp_dir,
                temp_directory=temp_dir / "temp",
                output_filename=None,  # Let it auto-generate
                audio_format="mp3",
                audio_quality="128",
                extract_audio_only=True,
                keep_video=False,
                download_thumbnail=False,
                download_subtitles=False,
                socket_timeout=30,
                retries=3,
                proxy_url=None,
                enable_progress_hooks=False
            )
            
            # Perform download
            result = await downloader.download_audio(config)
            
            # Verify download success
            assert result.success, f"Download should succeed: {result.error_message}"
            assert result.file_path is not None, "File path should be provided"
            assert result.file_path.exists(), "Downloaded file should exist"
            assert result.file_size is not None, "File size should be provided"
            assert result.file_size > 0, "File should have content"
            assert result.video_metadata is not None, "Metadata should be provided"
            
            print(f"Audio download test successful:")
            print(f"  File: {result.file_path}")
            print(f"  Size: {result.file_size} bytes")
            print(f"  Title: {result.video_metadata.title}")
            print(f"  Duration: {result.video_metadata.duration} seconds")
            print(f"  Processing time: {result.processing_time:.2f} seconds")
            
        except Exception as e:
            pytest.skip(f"Audio download test skipped due to network/YouTube issue: {e}")


# Pytest configuration for slow tests
def pytest_configure(config):
    """Configure pytest markers"""
    config.addinivalue_line("markers", "slow: marks tests as slow (deselect with '-m \"not slow\"')")


if __name__ == "__main__":
    # Run a simple test when executed directly
    import asyncio
    
    async def simple_test():
        container = get_container()
        await container.initialize()
        
        try:
            # Test URL validation
            downloader = container.youtube_downloader_service
            test_url = "https://www.youtube.com/watch?v=dQw4w9WgXcQ"
            
            print("Testing URL validation...")
            is_valid = await downloader.validate_url(test_url)
            print(f"URL validation result: {is_valid}")
            
            print("Testing video info extraction...")
            metadata = await downloader.get_video_info(test_url)
            print(f"Video title: {metadata.title}")
            print(f"Video duration: {metadata.duration} seconds")
            
        except Exception as e:
            print(f"Test failed: {e}")
        
        finally:
            await container.cleanup()
    
    asyncio.run(simple_test())
