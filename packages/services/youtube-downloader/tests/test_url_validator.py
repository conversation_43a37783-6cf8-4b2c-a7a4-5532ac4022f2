"""
Tests for URL validator service

Tests URL validation functionality including format validation,
domain checking, and video ID extraction.
"""

import pytest

from src.services.url_validator_service_impl import URLValidatorServiceImpl


class TestURLValidatorService:
    """Test cases for URL validator service"""

    @pytest.fixture
    def validator(self):
        """Create URL validator service instance"""
        allowed_domains = [
            "youtube.com",
            "youtu.be",
            "m.youtube.com",
            "www.youtube.com",
        ]
        return URLValidatorServiceImpl(allowed_domains=allowed_domains)

    def test_valid_youtube_urls(self, validator):
        """Test validation of valid YouTube URLs"""
        valid_urls = [
            "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
            "https://youtube.com/watch?v=dQw4w9WgXcQ",
            "https://youtu.be/dQw4w9WgXcQ",
            "https://m.youtube.com/watch?v=dQw4w9WgXcQ",
            "https://www.youtube.com/watch?v=dQw4w9WgXcQ&t=30s",
            "https://www.youtube.com/watch?v=dQw4w9WgXcQ&list=PLrAXtmRdnEQy8VJqQzNYaYzOzZyYzOzZy",
        ]

        for url in valid_urls:
            assert validator.is_youtube_url(url), f"URL should be valid: {url}"

    def test_invalid_youtube_urls(self, validator):
        """Test validation of invalid YouTube URLs"""
        invalid_urls = [
            "https://www.google.com",
            "https://vimeo.com/123456789",
            "not_a_url",
            "",
            "https://youtube.com",  # No video ID
            "https://www.youtube.com/channel/UCuAXFkgsw1L7xaCfnd5JJOw",  # Channel URL
            "https://www.youtube.com/playlist?list=PLrAXtmRdnEQy8VJqQzNYaYzOzZyYzOzZy",  # Playlist URL
        ]

        for url in invalid_urls:
            assert not validator.is_youtube_url(url), f"URL should be invalid: {url}"

    def test_extract_video_id(self, validator):
        """Test video ID extraction from URLs"""
        test_cases = [
            ("https://www.youtube.com/watch?v=dQw4w9WgXcQ", "dQw4w9WgXcQ"),
            ("https://youtu.be/dQw4w9WgXcQ", "dQw4w9WgXcQ"),
            ("https://www.youtube.com/watch?v=dQw4w9WgXcQ&t=30s", "dQw4w9WgXcQ"),
            ("https://m.youtube.com/watch?v=dQw4w9WgXcQ", "dQw4w9WgXcQ"),
        ]

        for url, expected_id in test_cases:
            video_id = validator.extract_video_id(url)
            assert video_id == expected_id, (
                f"Expected {expected_id}, got {video_id} for URL: {url}"
            )

    def test_extract_video_id_invalid_url(self, validator):
        """Test video ID extraction from invalid URLs"""
        invalid_urls = [
            "https://www.google.com",
            "not_a_url",
            "",
            "https://youtube.com",
        ]

        for url in invalid_urls:
            video_id = validator.extract_video_id(url)
            assert video_id is None, f"Should return None for invalid URL: {url}"

    def test_normalize_url(self, validator):
        """Test URL normalization"""
        test_cases = [
            (
                "https://youtu.be/dQw4w9WgXcQ",
                "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
            ),
            (
                "https://m.youtube.com/watch?v=dQw4w9WgXcQ",
                "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
            ),
            (
                "https://www.youtube.com/watch?v=dQw4w9WgXcQ&t=30s",
                "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
            ),
            (
                "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
                "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
            ),
        ]

        for input_url, expected_url in test_cases:
            normalized = validator.normalize_url(input_url)
            assert normalized == expected_url, (
                f"Expected {expected_url}, got {normalized}"
            )

    def test_normalize_url_invalid(self, validator):
        """Test URL normalization with invalid URLs"""
        invalid_urls = ["https://www.google.com", "not_a_url", ""]

        for url in invalid_urls:
            normalized = validator.normalize_url(url)
            assert normalized is None, f"Should return None for invalid URL: {url}"

    @pytest.mark.asyncio
    async def test_validate_url_async(self, validator):
        """Test async URL validation"""
        valid_url = "https://www.youtube.com/watch?v=dQw4w9WgXcQ"
        invalid_url = "https://www.google.com"

        assert await validator.validate_url(valid_url)
        assert not await validator.validate_url(invalid_url)
